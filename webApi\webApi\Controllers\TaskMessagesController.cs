using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.SignalR;
using System.Text.Json;
using webApi.Models;
using webApi.Hubs;
using webApi.Services;
using TaskModel = webApi.Models.Task;

namespace webApi.Controllers;

/// <summary>
/// متحكم رسائل المهام - للمحادثات الفورية المرتبطة بالمهام
/// </summary>
[Route("api/[controller]")]
[ApiController]
[Produces("application/json")]
public class TaskMessagesController : ControllerBase
{
    private readonly TasksDbContext _context;
    private readonly IHubContext<ChatHub> _hubContext;
    private readonly ILogger<TaskMessagesController> _logger;
    private readonly INotificationService _notificationService;

    public TaskMessagesController(
        TasksDbContext context,
        IHubContext<ChatHub> hubContext,
        ILogger<TaskMessagesController> logger,
        INotificationService notificationService)
    {
        _context = context;
        _hubContext = hubContext;
        _logger = logger;
        _notificationService = notificationService;
    }

   

    /// <summary>
    /// الحصول على جميع رسائل مهمة محددة
    /// </summary>
    /// <param name="taskId">معرف المهمة</param>
    /// <param name="page">رقم الصفحة (افتراضي: 1)</param>
    /// <param name="pageSize">حجم الصفحة (افتراضي: 50)</param>
    /// <returns>قائمة رسائل المهمة</returns>
    [HttpGet("task/{taskId}")]
    public async Task<ActionResult<IEnumerable<TaskMessageResponse>>> GetTaskMessages(
        int taskId, 
        int page = 1, 
        int pageSize = 50)
    {
        try
        {
            // التحقق من وجود المهمة
            var taskExists = await _context.Tasks.AnyAsync(t => t.Id == taskId && !t.IsDeleted);
            if (!taskExists)
            {
                return NotFound("المهمة غير موجودة");
            }

            // جلب الرسائل مع التصفح
            var messages = await _context.TaskMessages
                .Include(tm => tm.Sender)
                .Include(tm => tm.ReplyToMessage)
                .ThenInclude(rtm => rtm!.Sender)
                .Include(tm => tm.PinnedByNavigation)
                .Include(tm => tm.MarkedForFollowUpByNavigation)
                .Include(tm => tm.MessageReads)
                .Where(tm => tm.TaskId == taskId && !tm.IsDeleted)
                .OrderByDescending(tm => tm.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // تحويل إلى نموذج الاستجابة
            var response = messages.Select(m => MapToResponse(m, taskId)).ToList();

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في جلب رسائل المهمة {TaskId}", taskId);
            return StatusCode(500, "خطأ في الخادم");
        }
    }

    /// <summary>
    /// إرسال رسالة جديدة للمهمة
    /// </summary>
    /// <param name="request">بيانات الرسالة</param>
    /// <returns>الرسالة المرسلة</returns>
    [HttpPost]
    public async Task<ActionResult<TaskMessageResponse>> SendMessage([FromBody] SendTaskMessageRequest request)
    {
        try
        {
            // التحقق من صحة البيانات
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // التحقق من وجود المهمة
            var taskExists = await _context.Tasks.AnyAsync(t => t.Id == request.TaskId && !t.IsDeleted);
            if (!taskExists)
            {
                return NotFound("المهمة غير موجودة");
            }

            // الحصول على معرف المستخدم الحالي (يجب تنفيذ نظام المصادقة)
            var currentUserId = GetCurrentUserId();
            if (currentUserId == null)
            {
                return Unauthorized("يجب تسجيل الدخول أولاً");
            }

            // إنشاء الرسالة الجديدة
            var message = new TaskMessage
            {
                TaskId = request.TaskId,
                SenderId = currentUserId.Value,
                Content = request.Content,
                ContentType = request.ContentType,
                ReplyToMessageId = request.ReplyToMessageId,
                Priority = request.Priority,
                CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                MentionedUserIds = request.MentionedUserIds?.Count > 0 
                    ? JsonSerializer.Serialize(request.MentionedUserIds) 
                    : null,
                AttachmentIds = request.AttachmentIds?.Count > 0 
                    ? JsonSerializer.Serialize(request.AttachmentIds) 
                    : null
            };

            _context.TaskMessages.Add(message);
            await _context.SaveChangesAsync();

            // تحميل البيانات المرتبطة
            await _context.Entry(message)
                .Reference(m => m.Sender)
                .LoadAsync();

            if (message.ReplyToMessageId.HasValue)
            {
                await _context.Entry(message)
                    .Reference(m => m.ReplyToMessage)
                    .LoadAsync();
            }

            // إرسال إشعارات للمستخدمين المعنيين
            await CreateTaskMessageNotifications(message, request.TaskId, currentUserId.Value);

            // إرسال الرسالة عبر SignalR
            var response = MapToResponse(message, request.TaskId);
            await _hubContext.Clients.Group($"Task_{request.TaskId}")
                .SendAsync("ReceiveTaskMessage", response);

            // إنشاء حالات قراءة للمستخدمين المخولين
            await CreateMessageReadStatusesAsync(message.Id, request.TaskId, currentUserId.Value);

            _logger.LogInformation("تم إرسال رسالة جديدة للمهمة {TaskId} من المستخدم {UserId}", 
                request.TaskId, currentUserId);

            return CreatedAtAction(nameof(GetMessage), new { id = message.Id }, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في إرسال رسالة للمهمة {TaskId}", request.TaskId);
            return StatusCode(500, "خطأ في الخادم");
        }
    }

    /// <summary>
    /// الحصول على رسالة محددة
    /// </summary>
    /// <param name="id">معرف الرسالة</param>
    /// <returns>تفاصيل الرسالة</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<TaskMessageResponse>> GetMessage(int id)
    {
        try
        {
            var message = await _context.TaskMessages
                .Include(tm => tm.Sender)
                .Include(tm => tm.ReplyToMessage)
                .ThenInclude(rtm => rtm!.Sender)
                .Include(tm => tm.PinnedByNavigation)
                .Include(tm => tm.MarkedForFollowUpByNavigation)
                .Include(tm => tm.MessageReads)
                .FirstOrDefaultAsync(tm => tm.Id == id && !tm.IsDeleted);

            if (message == null)
            {
                return NotFound("الرسالة غير موجودة");
            }

            var response = MapToResponse(message, message.TaskId);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في جلب الرسالة {MessageId}", id);
            return StatusCode(500, "خطأ في الخادم");
        }
    }

    /// <summary>
    /// تحديث رسالة موجودة
    /// </summary>
    /// <param name="id">معرف الرسالة</param>
    /// <param name="request">البيانات المحدثة</param>
    /// <returns>الرسالة المحدثة</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<TaskMessageResponse>> UpdateMessage(int id, [FromBody] UpdateTaskMessageRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var message = await _context.TaskMessages
                .Include(tm => tm.Sender)
                .FirstOrDefaultAsync(tm => tm.Id == id && !tm.IsDeleted);

            if (message == null)
            {
                return NotFound("الرسالة غير موجودة");
            }

            // التحقق من صلاحية التعديل (المرسل فقط)
            var currentUserId = GetCurrentUserId();
            if (currentUserId == null || message.SenderId != currentUserId)
            {
                return StatusCode(403, new { message = "لا يمكنك تعديل هذه الرسالة" });
            }

            // تحديث الرسالة
            message.Content = request.Content;
            message.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            message.IsEdited = true;

            await _context.SaveChangesAsync();

            // إرسال التحديث عبر SignalR
            var response = MapToResponse(message, message.TaskId);
            await _hubContext.Clients.Group($"Task_{message.TaskId}")
                .SendAsync("TaskMessageUpdated", response);

            _logger.LogInformation("تم تحديث الرسالة {MessageId} من المستخدم {UserId}", 
                id, currentUserId);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في تحديث الرسالة {MessageId}", id);
            return StatusCode(500, "خطأ في الخادم");
        }
    }

    /// <summary>
    /// حذف رسالة
    /// </summary>
    /// <param name="id">معرف الرسالة</param>
    /// <returns>نتيجة العملية</returns>
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteMessage(int id)
    {
        try
        {
            var message = await _context.TaskMessages
                .FirstOrDefaultAsync(tm => tm.Id == id && !tm.IsDeleted);

            if (message == null)
            {
                return NotFound("الرسالة غير موجودة");
            }

            // التحقق من صلاحية الحذف (المرسل فقط أو المدير)
            var currentUserId = GetCurrentUserId();
            if (currentUserId == null || message.SenderId != currentUserId)
            {
                return StatusCode(403, new { message = "لا يمكنك حذف هذه الرسالة" });
            }

            // حذف منطقي
            message.IsDeleted = true;
            message.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            await _context.SaveChangesAsync();

            // إرسال إشعار الحذف عبر SignalR
            await _hubContext.Clients.Group($"Task_{message.TaskId}")
                .SendAsync("TaskMessageDeleted", new { messageId = id });

            _logger.LogInformation("تم حذف الرسالة {MessageId} من المستخدم {UserId}",
                id, currentUserId);

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في حذف الرسالة {MessageId}", id);
            return StatusCode(500, "خطأ في الخادم");
        }
    }

    /// <summary>
    /// تثبيت أو إلغاء تثبيت رسالة
    /// </summary>
    /// <param name="id">معرف الرسالة</param>
    /// <param name="request">بيانات التثبيت</param>
    /// <returns>الرسالة المحدثة</returns>
    [HttpPatch("{id}/pin")]
    public async Task<ActionResult<TaskMessageResponse>> PinMessage(int id, [FromBody] PinTaskMessageRequest request)
    {
        try
        {
            var message = await _context.TaskMessages
                .Include(tm => tm.Sender)
                .FirstOrDefaultAsync(tm => tm.Id == id && !tm.IsDeleted);

            if (message == null)
            {
                return NotFound("الرسالة غير موجودة");
            }

            var currentUserId = GetCurrentUserId();
            if (currentUserId == null)
            {
                return Unauthorized("يجب تسجيل الدخول أولاً");
            }

            // تحديث حالة التثبيت
            message.IsPinned = request.IsPinned;
            if (request.IsPinned)
            {
                message.PinnedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                message.PinnedBy = currentUserId.Value;
            }
            else
            {
                message.PinnedAt = null;
                message.PinnedBy = null;
            }

            message.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            await _context.SaveChangesAsync();

            // إرسال التحديث عبر SignalR
            var response = MapToResponse(message, message.TaskId);
            await _hubContext.Clients.Group($"Task_{message.TaskId}")
                .SendAsync("TaskMessagePinned", response);

            _logger.LogInformation("تم {Action} الرسالة {MessageId} من المستخدم {UserId}",
                request.IsPinned ? "تثبيت" : "إلغاء تثبيت", id, currentUserId);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في تثبيت الرسالة {MessageId}", id);
            return StatusCode(500, "خطأ في الخادم");
        }
    }

    /// <summary>
    /// تحديد رسالة للمتابعة
    /// </summary>
    /// <param name="id">معرف الرسالة</param>
    /// <param name="request">بيانات المتابعة</param>
    /// <returns>الرسالة المحدثة</returns>
    [HttpPatch("{id}/follow-up")]
    public async Task<ActionResult<TaskMessageResponse>> MarkForFollowUp(int id, [FromBody] MarkForFollowUpRequest request)
    {
        try
        {
            var message = await _context.TaskMessages
                .Include(tm => tm.Sender)
                .FirstOrDefaultAsync(tm => tm.Id == id && !tm.IsDeleted);

            if (message == null)
            {
                return NotFound("الرسالة غير موجودة");
            }

            var currentUserId = GetCurrentUserId();
            if (currentUserId == null)
            {
                return Unauthorized("يجب تسجيل الدخول أولاً");
            }

            // تحديث حالة المتابعة
            message.IsMarkedForFollowUp = request.IsMarkedForFollowUp;
            if (request.IsMarkedForFollowUp)
            {
                message.FollowUpAt = request.FollowUpAt;
                message.MarkedForFollowUpBy = currentUserId.Value;
            }
            else
            {
                message.FollowUpAt = null;
                message.MarkedForFollowUpBy = null;
            }

            message.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            await _context.SaveChangesAsync();

            // إرسال التحديث عبر SignalR
            var response = MapToResponse(message, message.TaskId);
            await _hubContext.Clients.Group($"Task_{message.TaskId}")
                .SendAsync("TaskMessageMarkedForFollowUp", response);

            _logger.LogInformation("تم {Action} الرسالة {MessageId} للمتابعة من المستخدم {UserId}",
                request.IsMarkedForFollowUp ? "تحديد" : "إلغاء تحديد", id, currentUserId);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في تحديد الرسالة للمتابعة {MessageId}", id);
            return StatusCode(500, "خطأ في الخادم");
        }
    }

    /// <summary>
    /// تحديد رسالة كمقروءة
    /// </summary>
    /// <param name="id">معرف الرسالة</param>
    /// <returns>نتيجة العملية</returns>
    [HttpPost("{id}/read")]
    public async Task<IActionResult> MarkAsRead(int id)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            if (currentUserId == null)
            {
                return Unauthorized("يجب تسجيل الدخول أولاً");
            }

            // التحقق من وجود الرسالة
            var messageExists = await _context.TaskMessages
                .AnyAsync(tm => tm.Id == id && !tm.IsDeleted);

            if (!messageExists)
            {
                return NotFound("الرسالة غير موجودة");
            }

            // التحقق من وجود حالة قراءة مسبقة
            var existingRead = await _context.TaskMessageReads
                .FirstOrDefaultAsync(tmr => tmr.MessageId == id && tmr.UserId == currentUserId.Value);

            if (existingRead == null)
            {
                // إنشاء حالة قراءة جديدة
                var messageRead = new TaskMessageRead
                {
                    MessageId = id,
                    UserId = currentUserId.Value,
                    ReadAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    IsDelivered = true,
                    DeliveredAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.TaskMessageReads.Add(messageRead);
                await _context.SaveChangesAsync();

                // إرسال إشعار القراءة عبر SignalR
                await _hubContext.Clients.Group($"Task_{await GetTaskIdByMessageId(id)}")
                    .SendAsync("MessageRead", new { messageId = id, userId = currentUserId.Value });

                _logger.LogInformation("تم تحديد الرسالة {MessageId} كمقروءة من المستخدم {UserId}",
                    id, currentUserId);
            }

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في تحديد الرسالة كمقروءة {MessageId}", id);
            return StatusCode(500, "خطأ في الخادم");
        }
    }

    /// <summary>
    /// الحصول على الرسائل المثبتة لمهمة
    /// </summary>
    /// <param name="taskId">معرف المهمة</param>
    /// <returns>قائمة الرسائل المثبتة</returns>
    [HttpGet("task/{taskId}/pinned")]
    public async Task<ActionResult<IEnumerable<TaskMessageResponse>>> GetPinnedMessages(int taskId)
    {
        try
        {
            var messages = await _context.TaskMessages
                .Include(tm => tm.Sender)
                .Include(tm => tm.PinnedByNavigation)
                .Include(tm => tm.MessageReads)
                .Where(tm => tm.TaskId == taskId && tm.IsPinned && !tm.IsDeleted)
                .OrderByDescending(tm => tm.PinnedAt)
                .ToListAsync();

            var response = messages.Select(m => MapToResponse(m, taskId)).ToList();
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في جلب الرسائل المثبتة للمهمة {TaskId}", taskId);
            return StatusCode(500, "خطأ في الخادم");
        }
    }

    /// <summary>
    /// الحصول على جميع رسائل المهام
    /// </summary>
    /// <returns>قائمة بجميع الرسائل</returns>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<TaskMessageResponse>>> GetAllTaskMessages()
    {
        try
        {
            var messages = await _context.TaskMessages
                .Include(tm => tm.Sender)
                .Include(tm => tm.ReplyToMessage)
                .ThenInclude(rtm => rtm!.Sender)
                .Include(tm => tm.PinnedByNavigation)
                .Include(tm => tm.MarkedForFollowUpByNavigation)
                .Include(tm => tm.MessageReads)
                .Where(tm => !tm.IsDeleted)
                .OrderByDescending(tm => tm.CreatedAt)
                .ToListAsync();

            var response = messages.Select(m => MapToResponse(m, m.TaskId)).ToList();
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في جلب جميع رسائل المهام");
            return StatusCode(500, "خطأ في الخادم");
        }
    }

    // الوظائف المساعدة

    /// <summary>
    /// الحصول على معرف المستخدم الحالي
    /// يجب تنفيذ هذه الوظيفة حسب نظام المصادقة المستخدم
    /// </summary>
    /// <returns>معرف المستخدم أو null</returns>
    private int? GetCurrentUserId()
    {
        // TODO: تنفيذ الحصول على معرف المستخدم من JWT أو Session
        // هذا مثال مؤقت - يجب استبداله بالتنفيذ الفعلي
        var userIdClaim = User.FindFirst("UserId")?.Value;
        if (int.TryParse(userIdClaim, out int userId))
        {
            return userId;
        }

        // للاختبار فقط - إرجاع المستخدم الأول
        return 1;
    }

    /// <summary>
    /// تحويل TaskMessage إلى TaskMessageResponse
    /// </summary>
    /// <param name="message">الرسالة</param>
    /// <param name="taskId">معرف المهمة</param>
    /// <returns>نموذج الاستجابة</returns>
    private TaskMessageResponse MapToResponse(TaskMessage message, int taskId)
    {
        var currentUserId = GetCurrentUserId();

        return new TaskMessageResponse
        {
            Id = message.Id,
            TaskId = taskId,
            SenderId = message.SenderId,
            SenderName = message.Sender?.Name ?? "مستخدم غير معروف",
            Content = message.Content,
            ContentType = message.ContentType,
            ReplyToMessageId = message.ReplyToMessageId,
            CreatedAt = message.CreatedAt,
            UpdatedAt = message.UpdatedAt,
            IsDeleted = message.IsDeleted,
            IsPinned = message.IsPinned,
            PinnedAt = message.PinnedAt,
            PinnedByName = message.PinnedByNavigation?.Name,
            Priority = message.Priority,
            IsMarkedForFollowUp = message.IsMarkedForFollowUp,
            FollowUpAt = message.FollowUpAt,
            MarkedForFollowUpByName = message.MarkedForFollowUpByNavigation?.Name,
            IsEdited = message.IsEdited,
            MentionedUserIds = !string.IsNullOrEmpty(message.MentionedUserIds)
                ? JsonSerializer.Deserialize<List<int>>(message.MentionedUserIds)
                : null,
            AttachmentIds = !string.IsNullOrEmpty(message.AttachmentIds)
                ? JsonSerializer.Deserialize<List<int>>(message.AttachmentIds)
                : null,
            ReadByCount = message.MessageReads?.Count ?? 0,
            TotalRecipients = GetTaskAccessUsersCount(taskId),
            IsReadByCurrentUser = currentUserId.HasValue &&
                (message.MessageReads?.Any(mr => mr.UserId == currentUserId.Value) ?? false),
            ReplyToMessage = message.ReplyToMessage != null
                ? MapToResponse(message.ReplyToMessage, taskId)
                : null
        };
    }

    /// <summary>
    /// إنشاء حالات قراءة للمستخدمين المخولين للوصول للمهمة
    /// </summary>
    /// <param name="messageId">معرف الرسالة</param>
    /// <param name="taskId">معرف المهمة</param>
    /// <param name="senderId">معرف المرسل</param>
    /// <returns>مهمة غير متزامنة</returns>
    private async System.Threading.Tasks.Task CreateMessageReadStatusesAsync(int messageId, int taskId, int senderId)
    {
        try
        {
            // جلب المستخدمين المخولين للوصول للمهمة
            var accessUserIds = await _context.TaskAccessUsers
                .Where(tau => tau.TaskId == taskId)
                .Select(tau => tau.UserId)
                .ToListAsync();

            // إضافة المرسل إذا لم يكن في القائمة
            if (!accessUserIds.Contains(senderId))
            {
                accessUserIds.Add(senderId);
            }

            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            var messageReads = new List<TaskMessageRead>();

            foreach (var userId in accessUserIds)
            {
                // المرسل يعتبر قرأ الرسالة تلقائياً
                var isRead = userId == senderId;

                messageReads.Add(new TaskMessageRead
                {
                    MessageId = messageId,
                    UserId = userId,
                    ReadAt = isRead ? currentTime : 0,
                    IsDelivered = true,
                    DeliveredAt = currentTime
                });
            }

            _context.TaskMessageReads.AddRange(messageReads);
            await _context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في إنشاء حالات قراءة الرسالة {MessageId}", messageId);
        }
    }

    /// <summary>
    /// الحصول على عدد المستخدمين المخولين للوصول للمهمة
    /// </summary>
    /// <param name="taskId">معرف المهمة</param>
    /// <returns>عدد المستخدمين</returns>
    private int GetTaskAccessUsersCount(int taskId)
    {
        try
        {
            return _context.TaskAccessUsers
                .Where(tau => tau.TaskId == taskId)
                .Count();
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// الحصول على معرف المهمة من معرف الرسالة
    /// </summary>
    /// <param name="messageId">معرف الرسالة</param>
    /// <returns>معرف المهمة</returns>
    private async System.Threading.Tasks.Task<int> GetTaskIdByMessageId(int messageId)
    {
        try
        {
            var message = await _context.TaskMessages
                .FirstOrDefaultAsync(tm => tm.Id == messageId);
            return message?.TaskId ?? 0;
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// إنشاء إشعارات للمستخدمين المعنيين برسالة المهمة
    /// </summary>
    private async System.Threading.Tasks.Task CreateTaskMessageNotifications(TaskMessage message, int taskId, int senderId)
    {
        try
        {
            _logger.LogInformation("🔍 بدء إنشاء إشعارات رسالة المهمة - TaskId: {TaskId}, SenderId: {SenderId}", taskId, senderId);

            // الحصول على بيانات المهمة
            var task = await _context.Tasks
                .Include(t => t.Creator)
                .Include(t => t.Assignee)
                .FirstOrDefaultAsync(t => t.Id == taskId);

            if (task == null)
            {
                _logger.LogWarning("⚠️ المهمة غير موجودة - TaskId: {TaskId}", taskId);
                return;
            }

            _logger.LogInformation("📋 بيانات المهمة - CreatorId: {CreatorId}, AssigneeId: {AssigneeId}",
                task.CreatorId, task.AssigneeId);

            // الحصول على المستخدمين المعنيين بالمهمة
            var userIds = new List<int>();

            // إضافة منشئ المهمة
            if (task.CreatorId != senderId)
            {
                userIds.Add(task.CreatorId);
                _logger.LogInformation("✅ تم إضافة منشئ المهمة للإشعارات - CreatorId: {CreatorId}", task.CreatorId);
            }
            else
            {
                _logger.LogInformation("⏭️ تم تجاهل منشئ المهمة (هو المرسل) - CreatorId: {CreatorId}", task.CreatorId);
            }

            // إضافة المسند له
            if (task.AssigneeId.HasValue &&
                task.AssigneeId.Value != senderId &&
                !userIds.Contains(task.AssigneeId.Value))
            {
                userIds.Add(task.AssigneeId.Value);
                _logger.LogInformation("✅ تم إضافة المسند له للإشعارات - AssigneeId: {AssigneeId}", task.AssigneeId.Value);
            }
            else if (task.AssigneeId.HasValue)
            {
                _logger.LogInformation("⏭️ تم تجاهل المسند له - AssigneeId: {AssigneeId}, السبب: {Reason}",
                    task.AssigneeId.Value,
                    task.AssigneeId.Value == senderId ? "هو المرسل" : "موجود مسبقاً");
            }

            // إضافة المستخدمين الذين لهم وصول للمهمة
            var accessUsers = await _context.TaskAccessUsers
                .Where(au => au.TaskId == taskId &&
                           au.UserId != senderId &&
                           !userIds.Contains(au.UserId))
                .Select(au => au.UserId)
                .ToListAsync();

            _logger.LogInformation("📊 المستخدمون في task_access_users - العدد: {Count}, القائمة: [{UserIds}]",
                accessUsers.Count, string.Join(", ", accessUsers));

            userIds.AddRange(accessUsers);

            _logger.LogInformation("🎯 إجمالي المستخدمين المستهدفين للإشعارات - العدد: {Count}, القائمة: [{UserIds}]",
                userIds.Count, string.Join(", ", userIds));

            // إرسال الإشعارات
            if (userIds.Count > 0)
            {
                var senderUser = await _context.Users.FindAsync(senderId);
                var senderName = senderUser?.Name ?? "مستخدم";

                _logger.LogInformation("📤 إرسال إشعارات للمستخدمين - المرسل: {SenderName} ({SenderId})", senderName, senderId);

                await _notificationService.CreateAndSendNotificationsAsync(
                    userIds,
                    "رسالة جديدة في مهمة",
                    $"المهمة رقم #{taskId}: أرسل {senderName} رسالة في المهمة '{task.Title}'",
                    "task_message_received",
                    taskId
                );

                _logger.LogInformation("✅ تم إرسال الإشعارات بنجاح للمستخدمين: [{UserIds}]", string.Join(", ", userIds));
            }
            else
            {
                _logger.LogWarning("⚠️ لا يوجد مستخدمين لإرسال إشعارات لهم - TaskId: {TaskId}, SenderId: {SenderId}", taskId, senderId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ خطأ في إنشاء إشعارات رسالة المهمة - MessageId: {MessageId}, TaskId: {TaskId}, SenderId: {SenderId}",
                message.Id, taskId, senderId);
        }
    }
}
